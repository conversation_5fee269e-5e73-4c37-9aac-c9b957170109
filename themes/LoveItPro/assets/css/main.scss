// LoveItPro Theme Main Styles
// ===========================

// Import Bootstrap if needed
@import url('https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css');

// Variables
// ---------
:root {
  // Colors
  --primary: #007bff;
  --secondary: #6c757d;
  --success: #28a745;
  --danger: #dc3545;
  --warning: #ffc107;
  --info: #17a2b8;
  --light: #f8f9fa;
  --dark: #343a40;

  // Typography
  --font-family-base: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --font-size-base: 1rem;
  --line-height-base: 1.6;

  // Spacing
  --spacer: 1rem;
  --border-radius: 0.375rem;
  --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --transition: all 0.15s ease-in-out;
  --border-color: #dee2e6;
}

// Base Styles
// -----------
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--dark);
  background-color: #fff;
  transition: var(--transition);
  margin: 0;
  padding: 0;
}

// Ensure main content has proper spacing
.main-content {
  min-height: 60vh;
  padding: 2rem 0;
}

// Fix container spacing
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

// Basic typography
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
}

p {
  margin-bottom: 1rem;
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);

  &:hover {
    color: var(--primary);
    text-decoration: underline;
  }
}

// Utility classes
.text-decoration-none {
  text-decoration: none !important;
}

.shadow-sm {
  box-shadow: var(--box-shadow) !important;
}

// Header Styles
// -------------
.site-header {
  .navbar {
    padding: 1rem 0;
    transition: var(--transition);
    
    .navbar-brand {
      font-weight: 700;
      font-size: 1.5rem;
      
      .logo-img {
        height: 40px;
        width: auto;
      }
    }
    
    .nav-link {
      font-weight: 500;
      padding: 0.5rem 1rem;
      transition: var(--transition);
      
      &:hover, &.active {
        color: var(--primary);
      }
    }
  }
}

// Hero Carousel
// -------------
.hero-carousel {
  position: relative;
  height: 500px;
  overflow: hidden;
  
  .carousel-item {
    height: 500px;
    position: relative;
  }
  
  .carousel-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  .carousel-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
  }
  
  .carousel-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    color: white;
  }
  
  .carousel-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    
    a {
      color: inherit;
      text-decoration: none;
      
      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
  
  .carousel-excerpt {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }
  
  .carousel-meta {
    margin-bottom: 1.5rem;
    
    .meta-item {
      margin-right: 1.5rem;
      opacity: 0.8;
      
      i {
        margin-right: 0.5rem;
      }
    }
  }
  
  .category-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background-color: var(--primary);
    color: white;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
  }
  
  .carousel-search {
    position: absolute;
    bottom: 2rem;
    left: 0;
    right: 0;
    z-index: 3;
  }
}

// Article Cards
// -------------
.article-card {
  .card {
    border: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    overflow: hidden;
    
    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
  }
  
  .card-img-container {
    position: relative;
    overflow: hidden;
    
    .card-img-top {
      height: 200px;
      object-fit: cover;
      transition: var(--transition);
    }
    
    &:hover .card-img-top {
      transform: scale(1.05);
    }
    
    .featured-badge {
      position: absolute;
      top: 1rem;
      left: 1rem;
      background-color: var(--warning);
      color: var(--dark);
      padding: 0.25rem 0.5rem;
      border-radius: var(--border-radius);
      font-size: 0.75rem;
      font-weight: 600;
    }
    
    .category-badge {
      position: absolute;
      top: 1rem;
      right: 1rem;
      
      a {
        background-color: var(--primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: var(--border-radius);
        font-size: 0.75rem;
        text-decoration: none;
      }
    }
  }
  
  .card-title {
    a {
      color: inherit;
      
      &:hover {
        color: var(--primary);
      }
    }
  }
  
  .card-meta {
    .meta-item {
      margin-right: 1rem;
      font-size: 0.875rem;
      color: var(--secondary);
      
      i {
        margin-right: 0.25rem;
      }
    }
  }
  
  .card-tags {
    .tag-link {
      margin-right: 0.5rem;
      text-decoration: none;
    }
  }
  
  .card-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &.featured {
    .card {
      border: 2px solid var(--warning);
    }
  }
  
  &.compact {
    .card-title {
      font-size: 1rem;
    }
  }
}

// Sidebar Styles
// --------------
.sidebar {
  .sidebar-widget {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    overflow: hidden;
    
    .widget-header {
      background-color: var(--light);
      padding: 1rem;
      border-bottom: 1px solid var(--border-color, #dee2e6);
      
      .widget-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        
        i {
          margin-right: 0.5rem;
          color: var(--primary);
        }
      }
    }
    
    .widget-content {
      padding: 1rem;
    }
  }
  
  // About Widget
  .about-widget {
    .avatar-img {
      width: 80px;
      height: 80px;
      object-fit: cover;
    }
    
    .about-stats {
      .stat-item {
        .stat-number {
          font-size: 1.5rem;
          font-weight: 700;
          color: var(--primary);
        }
        
        .stat-label {
          font-size: 0.875rem;
          color: var(--secondary);
        }
      }
    }
    
    .about-social {
      .social-link {
        display: inline-block;
        width: 40px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        border-radius: 50%;
        background-color: var(--light);
        color: var(--dark);
        margin: 0 0.25rem;
        transition: var(--transition);
        
        &:hover {
          background-color: var(--primary);
          color: white;
          transform: translateY(-2px);
        }
      }
    }
  }
  
  // Social Widget
  .social-widget {
    .social-link-item {
      margin-bottom: 0.5rem;
      
      .social-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        border-radius: var(--border-radius);
        text-decoration: none;
        color: white;
        transition: var(--transition);
        
        &:hover {
          transform: translateX(5px);
          box-shadow: var(--box-shadow);
        }
        
        .social-icon {
          width: 40px;
          text-align: center;
          font-size: 1.2rem;
        }
        
        .social-info {
          flex: 1;
          margin-left: 0.75rem;
          
          .social-name {
            font-weight: 600;
          }
          
          .social-description {
            font-size: 0.875rem;
            opacity: 0.8;
          }
        }
        
        .social-arrow {
          opacity: 0.6;
        }
      }
    }
  }
}

// Footer Styles
// -------------
.site-footer {
  background-color: var(--dark);
  color: white;
  padding: 3rem 0 1rem;
  margin-top: 4rem;
  
  .footer-content {
    .footer-section {
      margin-bottom: 2rem;
      
      h5 {
        margin-bottom: 1rem;
        color: white;
      }
      
      ul {
        list-style: none;
        padding: 0;
        
        li {
          margin-bottom: 0.5rem;
          
          a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: var(--transition);
            
            &:hover {
              color: white;
            }
          }
        }
      }
    }
  }
  
  .footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    margin-top: 2rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
  }
}

// Utilities
// ---------
.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  width: 50px;
  height: 50px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: var(--transition);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  
  &.show {
    opacity: 1;
    visibility: visible;
  }
  
  &:hover {
    background-color: var(--dark);
    transform: translateY(-2px);
  }
}

// Dark Mode
// ---------
[data-theme="dark"] {
  body {
    background-color: #1a1a1a;
    color: #e9ecef;
  }
  
  .sidebar-widget {
    background-color: #2d2d2d;
    color: #e9ecef;
    
    .widget-header {
      background-color: #404040;
      border-bottom-color: #555;
    }
  }
  
  .card {
    background-color: #2d2d2d;
    color: #e9ecef;
  }
  
  .navbar {
    background-color: #2d2d2d !important;
    
    .navbar-nav .nav-link {
      color: #e9ecef;
    }
  }
}

// Search Modal
// ------------
.search-modal {
  .search-results-list {
    max-height: 400px;
    overflow-y: auto;

    .search-result-item {
      padding: 1rem;
      border-bottom: 1px solid var(--border-color, #dee2e6);
      transition: var(--transition);

      &:hover {
        background-color: var(--light);
      }

      .result-title {
        font-weight: 600;
        margin-bottom: 0.5rem;

        a {
          color: inherit;
          text-decoration: none;

          &:hover {
            color: var(--primary);
          }
        }
      }

      .result-excerpt {
        color: var(--secondary);
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
      }

      .result-meta {
        font-size: 0.75rem;
        color: var(--secondary);

        .meta-item {
          margin-right: 1rem;
        }
      }
    }
  }
}

// Responsive Design
// -----------------
@media (max-width: 768px) {
  .hero-carousel {
    height: 400px;

    .carousel-item {
      height: 400px;
    }

    .carousel-title {
      font-size: 1.8rem;
    }
  }

  .article-card .card:hover {
    transform: none;
  }

  .sidebar {
    margin-top: 2rem;
  }
}
